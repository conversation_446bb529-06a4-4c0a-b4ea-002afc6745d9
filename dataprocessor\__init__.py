"""
DataProcessor - A clean, scalable Python library for data processing tasks.

This library provides utilities for common data processing operations including
data validation, transformation, and analysis.

Author: Your Name
License: MIT
Version: 0.1.0
"""

from .core import DataProcessor, DataTransformer
from .helpers import format_data, clean_data
from .validators import validate_data_format, validate_file_path
from .exceptions import DataProcessorError, ValidationError, FileNotFoundError

__version__ = "0.1.0"
__author__ = "Your Name"
__email__ = "<EMAIL>"
__license__ = "MIT"

# Define what gets imported when someone does "from dataprocessor import *"
__all__ = [
    "DataProcessor",
    "DataTransformer", 
    "format_data",
    "clean_data",
    "validate_data_format",
    "validate_file_path",
    "DataProcessorError",
    "ValidationError",
    "FileNotFoundError"
]
